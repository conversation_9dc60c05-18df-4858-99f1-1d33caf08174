/*
 * @Author: da<PERSON>i
 * @Date: 2022-01-12 14:05:56
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2023-08-07 17:12:07
 * @FilePath: \web-pc\src\pages\big-screen\main.js
 */
import Vue from "vue";
import App from "./App.vue";
import router from './router'
import store from './store'
import {loading,borderBox13,digitalFlop,capsuleChart,borderBox8,borderBox1} from '@jiaminghi/data-view'
import { Radio, Button, RadioGroup, Message, MessageBox, Notification, Loading, Form, FormItem, Input } from 'element-ui'
import Echart from './components/echart/index.vue'
import ItemWrap from './components/item-wrap/item-wrap.vue'
// import Message from './components/message/message.vue'
import Reacquire from './components/reacquire/reacquire.vue'
import Messages from './components/message/message'
import "vue-easytable/libs/theme-default/index.css";
import  '@/assets/css/public.scss'
import "@/assets/css/index.scss"


import * as filters from '@/directives/filters'

require('./mock/mock')//是否使用mock
Vue.config.productionTip = false;

// 自定义组件
Vue.component("Echart",Echart)
Vue.component("ItemWrap",ItemWrap)
// Vue.component("Message",Message)
Vue.component("Reacquire",Reacquire)
Vue.prototype.$Message =  Messages
// element组件
Vue.use(Radio);
Vue.use(Button);
Vue.use(RadioGroup);
Vue.use(Form);
Vue.use(FormItem);
Vue.use(Input);

// 全局挂载Element UI组件
Vue.prototype.$message = Message;
Vue.prototype.$msgbox = MessageBox;
Vue.prototype.$alert = MessageBox.alert;
Vue.prototype.$confirm = MessageBox.confirm;
Vue.prototype.$prompt = MessageBox.prompt;
Vue.prototype.$notify = Notification;
Vue.prototype.$loading = Loading.service;

// datav组件
Vue.use(loading)
Vue.use(borderBox13)
Vue.use(borderBox8)
Vue.use(digitalFlop)
Vue.use(capsuleChart)
// 全局数据过滤器
Object.keys(filters).forEach(k => Vue.filter(k, filters[k]));
new Vue({
  router,
  store,
  render: h => h(App)
}).$mount("#app");

// 引入BIGEMAP地图的CSS和JavaScript文件
const loadBigemap = new Promise((resolve, reject) => {
  const link = document.createElement('link');
  link.rel = 'stylesheet';
  link.href = 'http://***************:3000/bigemap.js/v2.1.0/bigemap.css'; // 替换为实际的CSS文件URL
  document.head.appendChild(link);

  const script = document.createElement('script');
  script.src = 'http://***************:3000/bigemap.js/v2.1.0/bigemap.js'; // 替换为实际的JavaScript文件URL
  script.onload = () => {
    // 设置全局配置
    if (window.BM) {
      window.BM.Config.HTTP_URL = 'http://***************:3000';
      window.BM.accessToken = 'pk.eyJ1IjoiY3VzXzg3a3J5a3Y4IiwiYSI6IjdmZzdsdWI0eDVtYmF6ZWRoOWxudmt2ZDEifQ.hbJM3hwBW1MVSGg3eiWdNQ';
    }
    resolve();
  };
  script.onerror = reject;
  document.body.appendChild(script);
});
