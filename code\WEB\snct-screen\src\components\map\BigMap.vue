<template>
  <div class="big-map-container">
    <div 
      :id="mapId" 
      class="map-wrapper"
      :style="{ width: width, height: height }"
    ></div>
    
    <!-- 地图控制按钮 -->
    <div class="map-controls" v-if="showControls">
      <div class="control-btn" @click="toggleLayer" title="切换图层">
        <i class="el-icon-picture"></i>
      </div>
      <div class="control-btn" @click="resetView" title="重置视图">
        <i class="el-icon-refresh"></i>
      </div>
      <div class="control-btn" @click="toggleFullscreen" title="全屏">
        <i class="el-icon-full-screen"></i>
      </div>
    </div>

    <!-- 图层切换面板 -->
    <div class="layer-panel" v-show="showLayerPanel">
      <div class="layer-item" 
           v-for="layer in availableLayers" 
           :key="layer.key"
           :class="{ active: currentLayer === layer.key }"
           @click="switchLayer(layer.key)">
        {{ layer.name }}
      </div>
    </div>
  </div>
</template>

<script>
import { mapConfig } from '@/utils/map/mapConfig'
import { mapUtils } from '@/utils/map/mapUtils'

export default {
  name: 'BigMap',
  props: {
    // 地图容器ID
    mapId: {
      type: String,
      default: 'bigemap-container'
    },
    // 地图宽度
    width: {
      type: String,
      default: '100%'
    },
    // 地图高度
    height: {
      type: String,
      default: '100%'
    },
    // 初始中心点
    center: {
      type: Array,
      default: () => [120.0, 30.0]
    },
    // 初始缩放级别
    zoom: {
      type: Number,
      default: 7
    },
    // 是否显示控制按钮
    showControls: {
      type: Boolean,
      default: true
    },
    // 地图配置选项
    options: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      map: null,
      currentLayer: 'satellite',
      showLayerPanel: false,
      availableLayers: [
        { key: 'satellite', name: '卫星图' },
        { key: 'sea', name: '海图' },
        { key: 'street', name: '街道图' }
      ],
      isFullscreen: false
    }
  },
  mounted() {
    this.initMap()
  },
  beforeDestroy() {
    if (this.map) {
      this.map.remove()
    }
  },
  methods: {
    // 初始化地图
    async initMap() {
      try {
        // 等待 bigemap 库加载完成
        await this.waitForBigemap()

        // 设置地图服务器地址和访问令牌
        BM.Config.HTTP_URL = mapConfig.mapHost
        BM.accessToken = mapConfig.accessToken || 'pk.eyJ1IjoiY3VzXzg3a3J5a3Y4IiwiYSI6IjdmZzdsdWI0eDVtYmF6ZWRoOWxudmt2ZDEifQ.hbJM3hwBW1MVSGg3eiWdNQ'

        // 创建地图实例 - 使用正确的mapid
        this.map = BM.map(this.mapId, 'bigemap.tian-map', {
          center: [this.center[0], this.center[1]],
          zoom: this.zoom,
          zoomControl: true,
          minZoom: 3,
          maxZoom: 18,
          attributionControl: false,
          ...this.options
        })

        // 添加默认图层
        this.addDefaultLayers()

        // 绑定地图事件
        this.bindMapEvents()

        // 设置地图边界以确保显示内容
        this.map.fitBounds([[15.792253494262695, 109.2919921875], [33.90689468383789, 126.826171875]])

        // 触发地图初始化完成事件
        this.$emit('map-ready', this.map)

      } catch (error) {
        console.error('地图初始化失败:', error)
        this.$emit('map-error', error)
      }
    },

    // 等待 bigemap 库加载
    waitForBigemap() {
      return new Promise((resolve, reject) => {
        if (window.BM) {
          resolve()
          return
        }

        let attempts = 0
        const maxAttempts = 50
        const checkInterval = setInterval(() => {
          attempts++
          if (window.BM) {
            clearInterval(checkInterval)
            resolve()
          } else if (attempts >= maxAttempts) {
            clearInterval(checkInterval)
            reject(new Error('Bigemap library failed to load'))
          }
        }, 100)
      })
    },

    // 添加默认图层
    addDefaultLayers() {
      // 创建卫星图层 - 使用官方案例的方式
      this.satelliteLayer = BM.tileLayer('bigemap.satellite')

      // 创建海图图层
      this.seaLayer = BM.tileLayer('bigemap.seaMap')

      // 默认添加卫星图层
      this.satelliteLayer.addTo(this.map)
    },

    // 绑定地图事件
    bindMapEvents() {
      this.map.on('click', (e) => {
        this.$emit('map-click', e)
      })
      
      this.map.on('zoom', (e) => {
        this.$emit('map-zoom', e)
      })
      
      this.map.on('moveend', (e) => {
        this.$emit('map-moveend', e)
      })
    },

    // 切换图层面板显示
    toggleLayer() {
      this.showLayerPanel = !this.showLayerPanel
    },

    // 切换地图图层
    switchLayer(layerKey) {
      if (this.currentLayer === layerKey) return

      // 移除当前图层
      if (this.currentLayer === 'satellite') {
        this.map.removeLayer(this.satelliteLayer)
      } else if (this.currentLayer === 'sea') {
        this.map.removeLayer(this.seaLayer)
      }

      // 添加新图层
      if (layerKey === 'satellite') {
        this.satelliteLayer.addTo(this.map)
      } else if (layerKey === 'sea') {
        this.seaLayer.addTo(this.map)
      }

      this.currentLayer = layerKey
      this.showLayerPanel = false
      this.$emit('layer-changed', layerKey)
    },

    // 重置视图
    resetView() {
      this.map.setView([this.center[0], this.center[1]], this.zoom)
    },

    // 切换全屏
    toggleFullscreen() {
      this.isFullscreen = !this.isFullscreen
      this.$emit('fullscreen-toggle', this.isFullscreen)
    },

    // 获取地图实例
    getMap() {
      return this.map
    },

    // 设置地图中心点
    setCenter(lng, lat, zoom) {
      if (this.map) {
        this.map.setView([lng, lat], zoom || this.map.getZoom())
      }
    },

    // 添加标记
    addMarker(lng, lat, options = {}) {
      if (!this.map) return null

      const marker = BM.marker([lng, lat], options)
      marker.addTo(this.map)
      return marker
    },

    // 移除标记
    removeMarker(marker) {
      if (this.map && marker) {
        this.map.removeLayer(marker)
      }
    }
  }
}
</script>

<style scoped>
.big-map-container {
  position: relative;
  width: 100%;
  height: 100%;
}

.map-wrapper {
  border: 2px solid #0ec6c4;
  border-radius: 4px;
}

.map-controls {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 1000;
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.control-btn {
  width: 32px;
  height: 32px;
  background: rgba(255, 255, 255, 0.9);
  border: 1px solid #ddd;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
}

.control-btn:hover {
  background: #fff;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.layer-panel {
  position: absolute;
  top: 10px;
  right: 50px;
  z-index: 1000;
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 8px;
  min-width: 100px;
}

.layer-item {
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 3px;
  transition: all 0.3s;
}

.layer-item:hover {
  background: #f5f5f5;
}

.layer-item.active {
  background: #409eff;
  color: white;
}
</style>
